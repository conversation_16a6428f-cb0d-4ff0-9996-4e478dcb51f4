package com.example.meals.mapper;

import com.example.meals.entity.EmailVerification;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 邮箱验证码Mapper接口
 */
@Mapper
public interface EmailVerificationMapper {
    
    /**
     * 插入验证码记录
     */
    @Insert("INSERT INTO email_verification (email, verification_code, type, status, create_time, expire_time) " +
            "VALUES (#{email}, #{verificationCode}, #{type}, #{status}, #{createTime}, #{expireTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(EmailVerification emailVerification);
    
    /**
     * 根据邮箱和类型查找最新的有效验证码
     */
    @Select("SELECT * FROM email_verification " +
            "WHERE email = #{email} AND type = #{type} AND status = 0 AND expire_time > NOW() " +
            "ORDER BY create_time DESC LIMIT 1")
    EmailVerification findLatestValidCode(@Param("email") String email, @Param("type") String type);
    
    /**
     * 根据邮箱、验证码和类型查找验证码记录
     */
    @Select("SELECT * FROM email_verification " +
            "WHERE email = #{email} AND verification_code = #{code} AND type = #{type} AND status = 0")
    EmailVerification findByEmailAndCode(@Param("email") String email, 
                                       @Param("code") String code, 
                                       @Param("type") String type);
    
    /**
     * 更新验证码状态
     */
    @Update("UPDATE email_verification SET status = #{status}, use_time = #{useTime} WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status, @Param("useTime") LocalDateTime useTime);
    
    /**
     * 使验证码失效（将同一邮箱同一类型的所有未使用验证码标记为已过期）
     */
    @Update("UPDATE email_verification SET status = 2 " +
            "WHERE email = #{email} AND type = #{type} AND status = 0")
    int invalidateCodesByEmailAndType(@Param("email") String email, @Param("type") String type);
    
    /**
     * 清理过期的验证码记录
     */
    @Delete("DELETE FROM email_verification WHERE expire_time < NOW() OR create_time < #{cutoffTime}")
    int deleteExpiredCodes(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    /**
     * 统计指定邮箱在指定时间内发送的验证码数量
     */
    @Select("SELECT COUNT(*) FROM email_verification " +
            "WHERE email = #{email} AND type = #{type} AND create_time > #{afterTime}")
    int countRecentCodes(@Param("email") String email, 
                        @Param("type") String type, 
                        @Param("afterTime") LocalDateTime afterTime);
    
    /**
     * 根据ID查找验证码记录
     */
    @Select("SELECT * FROM email_verification WHERE id = #{id}")
    EmailVerification findById(@Param("id") Long id);
    
    /**
     * 查找指定邮箱的所有验证码记录
     */
    @Select("SELECT * FROM email_verification WHERE email = #{email} ORDER BY create_time DESC")
    List<EmailVerification> findByEmail(@Param("email") String email);
}
