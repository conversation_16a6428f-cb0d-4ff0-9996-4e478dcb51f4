package com.example.meals.service.impl;

import com.example.meals.dto.LoginRequest;
import com.example.meals.dto.EmailCodeLoginRequest;
import com.example.meals.dto.RegisterRequest;
import com.example.meals.dto.UserResponse;
import com.example.meals.dto.request.ResetPasswordRequest;
import com.example.meals.entity.User;
import com.example.meals.mapper.UserMapper;
import com.example.meals.service.UserService;
import com.example.meals.service.EmailVerificationService;
import com.example.meals.common.Result;
import com.example.meals.utils.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.regex.Pattern;

/**
 * 用户服务实现类
 */
@Service
public class UserServiceImpl implements UserService {
    
    @Autowired
    private UserMapper userMapper;

    @Autowired
    private EmailVerificationService emailVerificationService;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private PasswordEncoder passwordEncoder;
    
    // 邮箱正则表达式
    private static final String EMAIL_PATTERN = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
    
    @Override
    public Result<UserResponse> register(RegisterRequest request) {
        // 参数验证
        if (!StringUtils.hasText(request.getUsername())) {
            return Result.badRequest("用户名不能为空");
        }
        if (!StringUtils.hasText(request.getPassword())) {
            return Result.badRequest("密码不能为空");
        }
        if (!StringUtils.hasText(request.getEmail())) {
            return Result.badRequest("邮箱不能为空");
        }
        if (!StringUtils.hasText(request.getVerificationCode())) {
            return Result.badRequest("邮箱验证码不能为空");
        }
        
        // 格式验证
        if (request.getUsername().length() < 3 || request.getUsername().length() > 20) {
            return Result.badRequest("用户名长度必须在3-20个字符之间");
        }
        if (request.getPassword().length() < 6) {
            return Result.badRequest("密码长度不能少于6位");
        }
        if (!Pattern.matches(EMAIL_PATTERN, request.getEmail())) {
            return Result.badRequest("邮箱格式不正确");
        }

        // 验证邮箱验证码
        Result<Void> verifyResult = emailVerificationService.verifyCode(
            request.getEmail(), request.getVerificationCode(), "REGISTER");
        if (!verifyResult.getSuccess()) {
            return Result.badRequest("邮箱验证码" + verifyResult.getMessage().replace("验证码", ""));
        }

        // 检查用户名是否已存在
        if (userMapper.countByUsername(request.getUsername()) > 0) {
            return Result.badRequest("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (userMapper.countByEmail(request.getEmail()) > 0) {
            return Result.badRequest("邮箱已被注册");
        }
        
        try {
            // 创建用户对象
            User user = new User();
            user.setUsername(request.getUsername());
            user.setPassword(passwordEncoder.encode(request.getPassword())); // 使用BCrypt加密密码
            user.setEmail(request.getEmail());
            user.setStatus(1); // 默认启用
            user.setCreateTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());
            
            // 保存用户
            int result = userMapper.insert(user);
            if (result > 0) {
                return Result.success("注册成功", new UserResponse(user));
            } else {
                return Result.error("注册失败，请重试");
            }
        } catch (Exception e) {
            return Result.error("注册失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<UserResponse> login(LoginRequest request) {
        // 参数验证
        if (!StringUtils.hasText(request.getEmailOrPhone())) {
            return Result.badRequest("邮箱或手机号不能为空");
        }
        if (!StringUtils.hasText(request.getPassword())) {
            return Result.badRequest("密码不能为空");
        }
        
        try {
            // 根据邮箱查询用户
            User user = userMapper.selectByEmail(request.getEmailOrPhone());
            if (user == null) {
                return Result.badRequest("用户不存在");
            }
            
            // 检查用户状态
            if (user.getStatus() == 0) {
                return Result.forbidden("账户已被禁用");
            }
            
            // 验证密码
            if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
                return Result.badRequest("密码错误");
            }

            // 生成JWT Token，支持记住我功能
            boolean rememberMe = request.getRememberMe() != null && request.getRememberMe();
            String token = jwtUtil.generateToken(user.getId(), user.getUsername(), "USER", rememberMe);

            // 创建响应对象，包含token
            UserResponse userResponse = new UserResponse(user);
            userResponse.setToken(token);

            return Result.success("登录成功", userResponse);
        } catch (Exception e) {
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    @Override
    public Result<UserResponse> loginWithEmailCode(EmailCodeLoginRequest request) {
        // 参数验证
        if (!StringUtils.hasText(request.getEmail())) {
            return Result.badRequest("邮箱地址不能为空");
        }
        if (!StringUtils.hasText(request.getVerificationCode())) {
            return Result.badRequest("验证码不能为空");
        }

        // 验证邮箱格式
        if (!Pattern.matches(EMAIL_PATTERN, request.getEmail())) {
            return Result.badRequest("邮箱格式不正确");
        }

        try {
            // 验证验证码（LOGIN类型）
            Result<Void> verifyResult = emailVerificationService.verifyCode(
                request.getEmail(),
                request.getVerificationCode(),
                "LOGIN"
            );

            if (!verifyResult.getSuccess()) {
                return Result.badRequest(verifyResult.getMessage());
            }

            // 根据邮箱查询用户
            User user = userMapper.selectByEmail(request.getEmail());
            if (user == null) {
                return Result.badRequest("用户不存在");
            }

            // 检查用户状态
            if (user.getStatus() == 0) {
                return Result.forbidden("账户已被禁用");
            }

            // 生成JWT Token，支持记住我功能
            boolean rememberMe = request.getRememberMe() != null && request.getRememberMe();
            String token = jwtUtil.generateToken(user.getId(), user.getUsername(), "USER", rememberMe);

            // 创建响应对象，包含token
            UserResponse userResponse = new UserResponse(user);
            userResponse.setToken(token);

            return Result.success("登录成功", userResponse);
        } catch (Exception e) {
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> checkUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return Result.badRequest("用户名不能为空");
        }
        
        boolean available = userMapper.countByUsername(username) == 0;
        return Result.success(available);
    }
    
    @Override
    public Result<Boolean> checkEmail(String email) {
        if (!StringUtils.hasText(email)) {
            return Result.badRequest("邮箱不能为空");
        }
        
        if (!Pattern.matches(EMAIL_PATTERN, email)) {
            return Result.badRequest("邮箱格式不正确");
        }
        
        boolean available = userMapper.countByEmail(email) == 0;
        return Result.success(available);
    }
    

    
    @Override
    public Result<UserResponse> getUserById(Long id) {
        if (id == null || id <= 0) {
            return Result.badRequest("用户ID不能为空");
        }
        
        User user = userMapper.selectById(id);
        if (user == null) {
            return Result.notFound("用户不存在");
        }
        
        return Result.success(new UserResponse(user));
    }

    @Override
    public String getUserAvatarPath(Long userId) {
        if (userId == null || userId <= 0) {
            return null;
        }

        try {
            User user = userMapper.selectById(userId);
            return user != null ? user.getAvatar() : null;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public Result<Void> updateUserAvatar(Long userId, String avatarPath) {
        if (userId == null || userId <= 0) {
            return Result.badRequest("用户ID不能为空");
        }

        try {
            User user = userMapper.selectById(userId);
            if (user == null) {
                return Result.notFound("用户不存在");
            }

            // 更新头像路径
            User updateUser = new User();
            updateUser.setId(userId);
            updateUser.setAvatar(avatarPath);
            updateUser.setUpdateTime(LocalDateTime.now());

            int result = userMapper.updateById(updateUser);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.error("头像更新失败");
            }
        } catch (Exception e) {
            return Result.error("头像更新失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Void> deleteUserAvatar(Long userId) {
        if (userId == null || userId <= 0) {
            return Result.badRequest("用户ID不能为空");
        }

        try {
            User user = userMapper.selectById(userId);
            if (user == null) {
                return Result.notFound("用户不存在");
            }

            // 删除头像路径
            User updateUser = new User();
            updateUser.setId(userId);
            updateUser.setAvatar(null);
            updateUser.setUpdateTime(LocalDateTime.now());

            int result = userMapper.updateById(updateUser);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.error("头像删除失败");
            }
        } catch (Exception e) {
            return Result.error("头像删除失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Void> resetPassword(ResetPasswordRequest request) {
        // 参数验证
        if (request == null) {
            return Result.badRequest("请求参数不能为空");
        }
        if (!StringUtils.hasText(request.getEmail())) {
            return Result.badRequest("邮箱不能为空");
        }
        if (!StringUtils.hasText(request.getVerificationCode())) {
            return Result.badRequest("验证码不能为空");
        }
        if (!StringUtils.hasText(request.getNewPassword())) {
            return Result.badRequest("新密码不能为空");
        }
        if (request.getNewPassword().length() < 6) {
            return Result.badRequest("密码长度不能少于6位");
        }

        // 验证邮箱格式
        String emailPattern = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
        if (!Pattern.matches(emailPattern, request.getEmail())) {
            return Result.badRequest("邮箱格式不正确");
        }

        try {
            // 验证验证码（RESET_PASSWORD类型）
            Result<Void> verifyResult = emailVerificationService.verifyCode(
                request.getEmail(),
                request.getVerificationCode(),
                "RESET_PASSWORD"
            );

            if (!verifyResult.getSuccess()) {
                return Result.badRequest(verifyResult.getMessage());
            }

            // 根据邮箱查询用户
            User user = userMapper.selectByEmail(request.getEmail());
            if (user == null) {
                return Result.badRequest("用户不存在");
            }

            // 检查用户状态
            if (user.getStatus() == 0) {
                return Result.forbidden("账户已被禁用");
            }

            // 更新密码
            User updateUser = new User();
            updateUser.setId(user.getId());
            updateUser.setPassword(passwordEncoder.encode(request.getNewPassword()));
            updateUser.setUpdateTime(LocalDateTime.now());

            int result = userMapper.updateById(updateUser);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.error("密码重置失败，请重试");
            }
        } catch (Exception e) {
            return Result.error("密码重置失败：" + e.getMessage());
        }
    }

}
