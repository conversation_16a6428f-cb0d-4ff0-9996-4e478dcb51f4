<template>
  <div class="avatar-upload">
    <!-- 头像显示区域 -->
    <div class="avatar-preview">
      <AvatarDisplay
        :avatar-url="previewUrl || currentAvatarUrl"
        :name="userName"
        :size="120"
        :clickable="true"
        :show-upload-overlay="true"
        @click="triggerFileInput"
      />
    </div>
    
    <!-- 文件输入 -->
    <input
      ref="fileInput"
      type="file"
      accept="image/jpeg,image/jpg,image/png,image/gif"
      style="display: none"
      @change="handleFileSelect"
    />
    
    <!-- 操作按钮 -->
    <div class="upload-actions">
      <button 
        type="button" 
        class="btn btn-primary"
        @click="triggerFileInput"
        :disabled="uploading"
      >
        <i class="icon-upload"></i>
        {{ uploading ? '上传中...' : '选择头像' }}
      </button>
      
      <button 
        v-if="currentAvatarUrl || previewUrl"
        type="button" 
        class="btn btn-outline-danger"
        @click="removeAvatar"
        :disabled="uploading"
      >
        <i class="icon-trash"></i>
        删除头像
      </button>
    </div>
    
    <!-- 上传提示 -->
    <div class="upload-tips">
      <p class="tip-text">
        支持 JPG、PNG、GIF 格式，文件大小不超过 10MB
      </p>
    </div>
    
    <!-- 图片裁剪模态框 -->
    <div v-if="showCropModal" class="crop-modal-overlay" @click="closeCropModal">
      <div class="crop-modal" @click.stop>
        <div class="crop-header">
          <h3>裁剪头像</h3>
          <button class="close-btn" @click="closeCropModal">&times;</button>
        </div>
        
        <div class="crop-body">
          <div class="crop-container">
            <canvas
              ref="cropCanvas"
              :width="cropSize"
              :height="cropSize"
              @mousedown="startCrop"
              @mousemove="doCrop"
              @mouseup="endCrop"
            ></canvas>
          </div>
          
          <div class="crop-preview">
            <div class="preview-title">预览</div>
            <canvas
              ref="previewCanvas"
              :width="100"
              :height="100"
            ></canvas>
          </div>
        </div>
        
        <div class="crop-footer">
          <button class="btn btn-secondary" @click="closeCropModal">取消</button>
          <button class="btn btn-primary" @click="confirmCrop">确认</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AvatarDisplay from './AvatarDisplay.vue'
import { message } from '../utils/message'
import { TokenManager } from '../utils/auth'

export default {
  name: 'AvatarUpload',
  components: {
    AvatarDisplay
  },
  props: {
    // 当前头像URL
    currentAvatarUrl: {
      type: String,
      default: null
    },
    // 用户名
    userName: {
      type: String,
      default: ''
    },
    // 用户类型 (user/admin)
    userType: {
      type: String,
      default: 'user'
    }
  },
  data() {
    return {
      uploading: false,
      previewUrl: null,
      selectedFile: null,
      showCropModal: false,
      cropSize: 300,
      cropData: {
        startX: 0,
        startY: 0,
        endX: 0,
        endY: 0,
        isDragging: false
      }
    }
  },
  methods: {
    triggerFileInput() {
      this.$refs.fileInput.click()
    },
    
    handleFileSelect(event) {
      const file = event.target.files[0]
      if (!file) return
      
      // 验证文件
      if (!this.validateFile(file)) {
        return
      }
      
      this.selectedFile = file
      
      // 显示预览
      const reader = new FileReader()
      reader.onload = (e) => {
        this.previewUrl = e.target.result
        // 可以选择是否启用裁剪功能
        // this.showCropModal = true
        // 直接上传
        this.uploadAvatar()
      }
      reader.readAsDataURL(file)
    },
    
    validateFile(file) {
      // 检查文件类型
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
      if (!allowedTypes.includes(file.type)) {
        message.error('只支持 JPG、PNG、GIF 格式的图片')
        return false
      }

      // 检查文件大小 (10MB)
      const maxSize = 10 * 1024 * 1024
      if (file.size > maxSize) {
        message.error('文件大小不能超过 10MB')
        return false
      }
      
      return true
    },
    
    async uploadAvatar() {
      if (!this.selectedFile) return
      
      this.uploading = true
      
      try {
        const formData = new FormData()
        formData.append('file', this.selectedFile)
        
        const endpoint = this.userType === 'admin' 
          ? '/api/files/avatar/admin' 
          : '/api/files/avatar/user'
        
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.getToken()}`
          },
          body: formData
        })
        
        const result = await response.json()
        
        if (result.success) {
          message.success('头像上传成功')
          this.$emit('upload-success', result.data)
          // 清理预览
          this.previewUrl = null
          this.selectedFile = null
        } else {
          message.error(result.message || '头像上传失败')
          this.previewUrl = null
        }
      } catch (error) {
        console.error('头像上传失败:', error)
        message.error('头像上传失败，请重试')
        this.previewUrl = null
      } finally {
        this.uploading = false
        // 清空文件输入
        this.$refs.fileInput.value = ''
      }
    },
    
    async removeAvatar() {
      try {
        const response = await fetch('/api/files/avatar', {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${this.getToken()}`,
            'Content-Type': 'application/json'
          }
        })
        
        const result = await response.json()
        
        if (result.success) {
          message.success('头像删除成功')
          this.$emit('remove-success')
          this.previewUrl = null
        } else {
          message.error(result.message || '头像删除失败')
        }
      } catch (error) {
        console.error('头像删除失败:', error)
        message.error('头像删除失败，请重试')
      }
    },
    
    // 裁剪相关方法（可选功能）
    startCrop(event) {
      const rect = this.$refs.cropCanvas.getBoundingClientRect()
      this.cropData.startX = event.clientX - rect.left
      this.cropData.startY = event.clientY - rect.top
      this.cropData.isDragging = true
    },
    
    doCrop(event) {
      if (!this.cropData.isDragging) return
      
      const rect = this.$refs.cropCanvas.getBoundingClientRect()
      this.cropData.endX = event.clientX - rect.left
      this.cropData.endY = event.clientY - rect.top
      
      // 重绘裁剪区域
      this.drawCropArea()
    },
    
    endCrop() {
      this.cropData.isDragging = false
    },
    
    drawCropArea() {
      // 裁剪区域绘制逻辑
    },
    
    confirmCrop() {
      // 确认裁剪逻辑
      this.showCropModal = false
      this.uploadAvatar()
    },
    
    closeCropModal() {
      this.showCropModal = false
      this.previewUrl = null
      this.selectedFile = null
      this.$refs.fileInput.value = ''
    },

    // {{ AURA-X: Add - 添加统一的token获取方法. Approval: 寸止(ID:1735555200). }}
    getToken() {
      return TokenManager.getToken()
    }
  }
}
</script>

<style scoped>
.avatar-upload {
  text-align: center;
}

.avatar-preview {
  margin-bottom: 20px;
}

.upload-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-bottom: 15px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-outline-danger {
  background-color: transparent;
  color: #dc3545;
  border: 1px solid #dc3545;
}

.btn-outline-danger:hover:not(:disabled) {
  background-color: #dc3545;
  color: white;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.upload-tips {
  margin-top: 10px;
}

.tip-text {
  font-size: 12px;
  color: #6c757d;
  margin: 0;
}

/* 裁剪模态框样式 */
.crop-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.crop-modal {
  background: white;
  border-radius: 8px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
}

.crop-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e1e5e9;
}

.crop-header h3 {
  margin: 0;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
}

.crop-body {
  padding: 20px;
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.crop-container {
  flex: 1;
}

.crop-preview {
  text-align: center;
}

.preview-title {
  margin-bottom: 10px;
  font-size: 14px;
  color: #6c757d;
}

.crop-footer {
  padding: 15px 20px;
  border-top: 1px solid #e1e5e9;
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}
</style>
