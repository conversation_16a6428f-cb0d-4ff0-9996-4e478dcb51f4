<template>
  <div class="dashboard-container">
    <!-- 侧边栏 -->
    <aside class="sidebar" :class="{ collapsed: sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="logo-container">
          <div class="logo-icon">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"/>
            </svg>
          </div>
          <span v-if="!sidebarCollapsed" class="logo-text">营养管家</span>
        </div>
        <button @click="toggleSidebar" class="sidebar-toggle">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
          </svg>
        </button>
      </div>

      <nav class="sidebar-nav">
        <div class="nav-section">
          <h3 v-if="!sidebarCollapsed" class="nav-section-title">主要功能</h3>
          <ul class="nav-list">
            <li class="nav-item">
              <router-link to="/dashboard" class="nav-link">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                </svg>
                <span v-if="!sidebarCollapsed" class="nav-text">总览</span>
              </router-link>
            </li>
            <li class="nav-item">
              <router-link to="/nutrition" class="nav-link">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2l3.09 6.26L22 9l-6.91.74L12 16l-3.09-6.26L2 9l6.91-.74L12 2z"/>
                </svg>
                <span v-if="!sidebarCollapsed" class="nav-text">营养分析</span>
              </router-link>
            </li>
            <li class="nav-item">
              <router-link to="/meals" class="nav-link">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8.1 13.34l2.83-2.83L3.91 3.5c-1.56 1.56-1.56 4.09 0 5.66l4.19 4.18zm6.78-1.81c1.53.71 3.68.21 5.27-1.38 1.91-1.91 2.28-4.65.81-6.12-1.46-1.46-4.2-1.1-6.12.81-1.59 1.59-2.09 3.74-1.38 5.27L3.7 19.87l1.41 1.41L12 14.41l6.88 6.88 1.41-1.41L13.41 13l1.47-1.47z"/>
                </svg>
                <span v-if="!sidebarCollapsed" class="nav-text">膳食记录</span>
              </router-link>
            </li>
            <li class="nav-item">
              <router-link to="/goals" class="nav-link">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                <span v-if="!sidebarCollapsed" class="nav-text">健康目标</span>
              </router-link>
            </li>
            <li class="nav-item">
              <router-link to="/reports" class="nav-link">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                </svg>
                <span v-if="!sidebarCollapsed" class="nav-text">健康报告</span>
              </router-link>
            </li>
          </ul>
        </div>

        <div class="nav-section">
          <h3 v-if="!sidebarCollapsed" class="nav-section-title">个人中心</h3>
          <ul class="nav-list">
            <li class="nav-item">
              <router-link to="/profile" class="nav-link">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
                <span v-if="!sidebarCollapsed" class="nav-text">个人资料</span>
              </router-link>
            </li>
            <li class="nav-item">
              <router-link to="/settings" class="nav-link">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                </svg>
                <span v-if="!sidebarCollapsed" class="nav-text">设置</span>
              </router-link>
            </li>
          </ul>
        </div>
      </nav>

      <div class="sidebar-footer">
        <div class="user-profile-mini">
          <div @click="triggerAvatarUpload" style="cursor: pointer;">
            <AvatarDisplay
              :avatar-url="getUserAvatarUrl(user?.avatar)"
              :name="user?.username"
              :size="sidebarCollapsed ? 32 : 40"
              :clickable="false"
              :show-upload-overlay="true"
            />
          </div>
          <div v-if="!sidebarCollapsed" class="user-info-mini">
            <div class="username">{{ user?.username }}</div>
            <button @click="handleLogout" class="logout-btn">退出</button>
          </div>
        </div>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <main class="main-content" :class="{ expanded: sidebarCollapsed }">
      <!-- 顶部工具栏 -->
      <header class="content-header">
        <div class="header-left">
          <h1 class="page-title">总览</h1>
          <p class="page-subtitle">您的健康数据一览</p>
        </div>
        <div class="header-right">
          <div class="header-actions">
            <button class="action-btn notification-btn" @click="toggleNotifications">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
              </svg>
              <span v-if="notificationCount > 0" class="notification-badge">{{ notificationCount }}</span>
            </button>
            <button class="action-btn search-btn" @click="toggleSearch">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
              </svg>
            </button>
          </div>
        </div>
      </header>

      <!-- 动态内容区域 -->
      <div class="content-body">
        <!-- 总览页面 -->
        <div class="tab-content overview-content">
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H19v2h-1.5v16.5c0 .83-.67 1.5-1.5 1.5h-9c-.83 0-1.5-.67-1.5-1.5V4H4.5V2h4.5c0-.83.67-1.5 1.5-1.5h3c.83 0 1.5.67 1.5 1.5h4.5z"/>
                </svg>
              </div>
              <div class="stat-content">
                <h3 class="stat-value">{{ todayMeals }}</h3>
                <p class="stat-label">今日膳食记录</p>
                <span class="stat-change positive">+2 较昨日</span>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2l3.09 6.26L22 9l-6.91.74L12 16l-3.09-6.26L2 9l6.91-.74L12 2z"/>
                </svg>
              </div>
              <div class="stat-content">
                <h3 class="stat-value">{{ todayCalories }}</h3>
                <p class="stat-label">今日卡路里摄入</p>
                <span class="stat-change negative">-150 较目标</span>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <div class="stat-content">
                <h3 class="stat-value">{{ completedGoals }}/{{ totalGoals }}</h3>
                <p class="stat-label">健康目标完成</p>
                <span class="stat-change positive">75% 完成率</span>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
                </svg>
              </div>
              <div class="stat-content">
                <h3 class="stat-value">{{ weeklyScore }}</h3>
                <p class="stat-label">本周健康评分</p>
                <span class="stat-change positive">+5 较上周</span>
              </div>
            </div>
          </div>

          <!-- 快速操作区域 -->
          <div class="quick-actions">
            <h2 class="section-title">快速操作</h2>
            <div class="actions-grid">
              <router-link to="/meals" class="quick-action-btn">
                <div class="action-icon">📝</div>
                <span class="action-text">记录膳食</span>
              </router-link>
              <router-link to="/nutrition" class="quick-action-btn">
                <div class="action-icon">🔍</div>
                <span class="action-text">营养分析</span>
              </router-link>
              <router-link to="/goals" class="quick-action-btn">
                <div class="action-icon">🎯</div>
                <span class="action-text">设置目标</span>
              </router-link>
              <router-link to="/reports" class="quick-action-btn">
                <div class="action-icon">📊</div>
                <span class="action-text">查看报告</span>
              </router-link>
            </div>
          </div>

          <!-- 最近活动 -->
          <div class="recent-activity">
            <h2 class="section-title">最近活动</h2>
            <div class="activity-list">
              <div class="activity-item">
                <div class="activity-icon">🥗</div>
                <div class="activity-content">
                  <h4>记录了午餐</h4>
                  <p>鸡胸肉沙拉 - 350卡路里</p>
                  <span class="activity-time">2小时前</span>
                </div>
              </div>
              <div class="activity-item">
                <div class="activity-icon">🎯</div>
                <div class="activity-content">
                  <h4>完成了每日蛋白质目标</h4>
                  <p>已达到120g蛋白质摄入</p>
                  <span class="activity-time">4小时前</span>
                </div>
              </div>
              <div class="activity-item">
                <div class="activity-icon">📊</div>
                <div class="activity-content">
                  <h4>生成了周报告</h4>
                  <p>本周营养摄入分析报告</p>
                  <span class="activity-time">1天前</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>



      <!-- 隐藏的头像上传文件输入 -->
      <input
        ref="avatarFileInput"
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/gif"
        style="display: none"
        @change="handleAvatarFileSelect"
      />
    </main>
  </div>
</template>



<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'
import { getAvatarUrl as getUserAvatarUrl } from '../utils/userApi'
import AvatarDisplay from '../components/AvatarDisplay.vue'
import { message } from '../utils/message'
import { TokenManager } from '../utils/auth'

const router = useRouter()
const { user, logout, updateUserState } = useAuth()

// 响应式数据
const sidebarCollapsed = ref(false)
const notificationCount = ref(3)

// 统计数据
const todayMeals = ref(3)
const todayCalories = ref(1650)
const completedGoals = ref(3)
const totalGoals = ref(4)
const weeklyScore = ref(85)

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}



const toggleNotifications = () => {
  // 切换通知面板
  console.log('Toggle notifications')
}

const toggleSearch = () => {
  // 切换搜索功能
  console.log('Toggle search')
}

// 处理登出
const handleLogout = () => {
  logout()
  updateUserState()
  router.push('/')
}



// 头像上传相关方法
const avatarFileInput = ref<HTMLInputElement>()
const isUploading = ref(false)

const triggerAvatarUpload = () => {
  // 防止重复触发
  if (isUploading.value) {
    return
  }

  // 触发文件选择
  if (avatarFileInput.value) {
    avatarFileInput.value.click()
  }
}

const handleAvatarFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  // 如果没有选择文件或正在上传，直接返回
  if (!file || isUploading.value) return

  // 验证文件
  if (!validateAvatarFile(file)) {
    return
  }

  isUploading.value = true

  try {
    // 上传头像
    const formData = new FormData()
    formData.append('file', file)

    const response = await fetch('http://localhost:8080/api/files/avatar/user', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${TokenManager.getToken()}`
      },
      body: formData
    })

    const result = await response.json()

    if (result.success) {
      // 更新头像路径
      if (user.value) {
        user.value.avatar = result.data
        // 同时更新localStorage中的用户信息
        const { UserManager } = await import('../utils/auth')
        UserManager.setUser(user.value)
      }
      message.success('头像上传成功')
    } else {
      message.error(result.message || '头像上传失败')
    }
  } catch (error) {
    message.error('头像上传失败，请重试')
  } finally {
    isUploading.value = false
    // 清空文件输入
    target.value = ''
  }
}

const validateAvatarFile = (file: File): boolean => {
  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
  if (!allowedTypes.includes(file.type)) {
    message.error('只支持 JPG、PNG、GIF 格式的图片')
    return false
  }

  // 检查文件大小 (10MB)
  const maxSize = 10 * 1024 * 1024
  if (file.size > maxSize) {
    message.error('文件大小不能超过 10MB')
    return false
  }

  return true
}

onMounted(() => {
  // 路由守卫已经验证了用户权限，这里不需要重复检查
  // 可以在这里执行其他初始化逻辑
})
</script>

<style scoped>
/* 全局样式 */
.dashboard-container {
  display: flex;
  min-height: 100vh;
  background: #f8fafc;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 侧边栏样式 */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
  color: white;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.logo-icon svg {
  width: 24px;
  height: 24px;
}

.logo-text {
  font-size: 1.25rem;
  font-weight: 700;
  white-space: nowrap;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background 0.2s;
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

.sidebar-toggle svg {
  width: 20px;
  height: 20px;
}

.sidebar-nav {
  padding: 1rem 0;
  flex: 1;
}

.nav-section {
  margin-bottom: 2rem;
}

.nav-section-title {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #94a3b8;
  margin: 0 0 1rem 1.5rem;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin: 0.25rem 0;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0.75rem 1.5rem;
  color: #cbd5e1;
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
  border-radius: 0;
  position: relative;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-item.active .nav-link,
.nav-link.router-link-active {
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
  border-right: 3px solid #3b82f6;
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.nav-text {
  font-weight: 500;
  white-space: nowrap;
}

.sidebar-footer {
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-profile-mini {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info-mini {
  flex: 1;
  min-width: 0;
}

.username {
  font-weight: 600;
  font-size: 0.875rem;
  color: white;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.logout-btn {
  background: #ef4444;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background 0.2s;
}

.logout-btn:hover {
  background: #dc2626;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.main-content.expanded {
  margin-left: 0;
}

.content-header {
  background: white;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-left h1 {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.header-left p {
  color: #64748b;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  background: none;
  border: 1px solid #e2e8f0;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.action-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.action-btn svg {
  width: 20px;
  height: 20px;
  color: #64748b;
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #ef4444;
  color: white;
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.content-body {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

/* 标签页内容样式 */
.tab-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #f1f5f9;
  transition: all 0.2s ease;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.stat-icon svg {
  width: 24px;
  height: 24px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.stat-label {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0 0 0.5rem 0;
}

.stat-change {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 12px;
}

.stat-change.positive {
  background: #dcfce7;
  color: #16a34a;
}

.stat-change.negative {
  background: #fef2f2;
  color: #dc2626;
}

/* 快速操作区域 */
.quick-actions {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.quick-action-btn {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  text-decoration: none;
  color: inherit;
  display: block;
}

.quick-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.action-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.action-text {
  font-weight: 600;
  color: #1e293b;
}

/* 最近活动 */
.recent-activity {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-radius: 12px;
  background: #f8fafc;
}

.activity-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.activity-content h4 {
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.activity-content p {
  color: #64748b;
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
}

.activity-time {
  color: #94a3b8;
  font-size: 0.75rem;
}

/* 其他功能页面样式 */
.nutrition-tools, .goals-grid, .reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.tool-card, .goal-card, .report-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #f1f5f9;
}

.tool-card h3, .goal-card h3, .report-card h3 {
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.search-box {
  display: flex;
  gap: 0.5rem;
}

.search-box input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
}

.search-btn, .tool-btn, .report-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}

.search-btn:hover, .tool-btn:hover, .report-btn:hover {
  background: #2563eb;
}

/* 进度条样式 */
.goal-progress {
  margin-top: 1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #f1f5f9;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.875rem;
  color: #64748b;
}

/* 膳食记录样式 */
.meals-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.add-meal-btn {
  background: #10b981;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}

.add-meal-btn:hover {
  background: #059669;
}

.meals-timeline {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.meal-entry {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #3b82f6;
}

.meal-time {
  font-size: 0.875rem;
  color: #3b82f6;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.meal-content h4 {
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.meal-content p {
  color: #64748b;
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
}

.meal-calories {
  background: #fef3c7;
  color: #d97706;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

/* 表单样式 */
.profile-form, .settings-sections {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
}

.form-group input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.save-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}

.save-btn:hover {
  background: #2563eb;
}

.settings-section {
  margin-bottom: 1.5rem;
}

.settings-section h3 {
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f1f5f9;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item label {
  font-weight: 500;
  color: #374151;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .sidebar {
    width: 240px;
  }

  .sidebar.collapsed {
    width: 70px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -280px;
    z-index: 1000;
    height: 100vh;
  }

  .sidebar.collapsed {
    left: 0;
    width: 280px;
  }

  .main-content {
    margin-left: 0;
  }

  .content-header {
    padding: 1rem;
  }

  .content-body {
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .actions-grid {
    grid-template-columns: 1fr;
  }

  .header-left h1 {
    font-size: 1.5rem;
  }

  .meals-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
}
</style>
